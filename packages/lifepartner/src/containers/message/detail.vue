<template>
  <Space direction="vertical" align="start" style="margin-top: 24px">
    <Breadcrumb
      :items="[
        {
          title: '消息中心',
          to: '/message/list'
        },
        {
          title: '消息详情'
        }
      ]"
    />
    <Space class="msg-detail-container" direction="vertical" size="24px">
      <Space v-loading="loading" class="main" direction="vertical" :size="24" align="start">
        <Space class="header max-width" justify="space-between" align="start">
          <Text type="h4" bold color="text-title">{{ messageDetail.subject }}</Text>
          <Text v-if="messageDetail.createTime" color="text-placeholder" style="line-height: 20px" bold>{{ dayjs(messageDetail.createTime).format('YY-MM-DD hh:mm') }}</Text>
        </Space>
        <!-- <Text> -->
        <!-- {{ messageDetail.body }} -->
        <!-- &emsp;&emsp; -->
        <!-- <Text v-if="messageDetail.link" bold color="primary" @click="openLink">立即查看</Text> -->
        <!-- </Text> -->
        <Text class="msg-body" v-html="messageDetail.body?.includes('<script') ? '消息格式有误，请联系客服' : messageDetail.body"></Text>
      </Space>
    </Space>
  </Space>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { useRoute } from 'vue-router'
  import {
    Space, Text, Breadcrumb, vLoading,
  } from '@xhs/delight'
  import dayjs from 'dayjs'
  import { IMsgReceiverDto, postCommonReadmsg } from '~/services/edith_post_common_readmsg'
  import { subsystemAlias } from '~/constants/message'

  const route = useRoute()

  const messageDetail = ref<IMsgReceiverDto>({})

  const loading = ref(false)
  const getMsgDetail = async () => {
    if (!route.query.id) {
      return
    }
    loading.value = true
    try {
      const res = await postCommonReadmsg({
        msgId: route.query.id as string,
        subsystemAlias,
      })
      messageDetail.value = res.msgReceiverDto ?? {}
    } finally {
      loading.value = false
    }
  }

  if (route.query.id) {
    getMsgDetail()
  }

  // const openLink = () => {
  //   if (messageDetail.value.link) {
  //     window.open(messageDetail.value.link)
  //   }
  // }

</script>

<style lang="stylus" scoped>
.msg-detail-container
  background: var(--color-bg)
  padding: 24px
  margin 0 auto
  width 1392px
  min-height 70vh
  .max-width
    width 100%
  .main
    flex 1
    width 1125px
  .header
    height 40px
    border-bottom 1px solid var(--color-line-divider)
  :deep(.msg-body a)
    text-decoration none
    color var(--color-primary)
    cursor pointer
    margin-left 16px
    font-weight 500
    &:hover
      text-decoration underline
</style>

<template>
  <div style="position: relative; cursor: pointer">
    <Icon :icon="Remind" style="color: #4D4D4D" @click="handleClick" />
    <Text v-if="unreadMsgCount" class="count">{{ handleMessageTipsCount(unreadMsgCount) }}</Text>
  </div>
</template>

<script lang="ts" setup>
  import { ref, defineEmits } from 'vue'
  import { Text, Icon } from '@xhs/delight'
  import { Remind } from '@xhs/delight/icons'
  import { postCommonFindmsg, IMessageReceiverDtoList } from '~/services/edith_post_common_findmsg'
  import { subsystemAlias } from '~/constants/message'
  import { handleMessageTipsCount } from '~/utils/help'

  const emit = defineEmits(['click'])

  const unreadMsgCount = ref(0)
  const msgList = ref<IMessageReceiverDtoList[]>([])

  const getMessageList = async () => {
    try {
      const res = await postCommonFindmsg({
        subsystemAlias,
      })
      unreadMsgCount.value = res?.unread ?? 0
      msgList.value = res?.messageReceiverDtoList ?? []
    } catch (error: any) {
      console.log(`报错位置：packages/lifepartner/src/containers/layout/AppHeader.vue，获取消息列表接口报错，报错信息：${error?.message}`)
    }
  }

  const handleClick = () => {
    emit('click')
  }

  getMessageList()
</script>

<style lang="stylus" scoped>
.count
  position: absolute;
  top: -2px;
  right: 6px;
  transform: translateY(-50%) translateX(100%);
  border-radius: 10px;
  color: #fff;
  font-size: 12px;
  height: 18px;
  line-height: 16px;
  padding: 0px 6px;
  text-align: center;
  white-space: nowrap;
  background-color: rgb(255, 82, 93);
  border: 1px solid rgb(255, 255, 255);
</style>

<template>
  <div class="app-header">
    <div class="life-app-content">
      <div class="logo-wrap">
        <img
          class="logo"
          src="https://picasso-static.xiaohongshu.com/picasso-editor/0768caaa6707ea3b1effe810b2cbceb02f37fe81"
        >
        <Text type="h5" bold>代运营服务商平台</Text>
      </div>
      <Space>
        <MessageHeader v-if="showUserInfo" @click="msgDrawerVisible = true" />
        <Dropdown v-if="showUserInfo" auto-close :offset="8">
          <div class="user-info">
            <Avatar :src="user?.image" size="small" />
            <Text ellipsis style="max-width: 120px;">{{ user?.providerName || '-' }}</Text>
            <Icon :icon="Down" />
          </div>
          <template #options>
            <Option label="退出登录" @click="logout" />
          </template>
        </Dropdown>
      </Space>
    </div>
    <MessageDrawer
      v-model:visible="msgDrawerVisible"
      @close="msgDrawerVisible = false"
    />
  </div>
</template>

<script lang="tsx" setup>
  import { computed, ref } from 'vue'
  import {
    Avatar, Text, Icon, Dropdown, Option, Space,
  } from '@xhs/delight'
  import { Down } from '@xhs/delight/icons'
  import { useStore } from 'vuex'

  import { logout } from 'shared/partner/sso'
  import { PartnerInfo } from 'shared/partner/services/user'
  import MessageDrawer from '../message/Drawer.vue'
  import MessageHeader from '../message/header.vue'

  const store = useStore()
  withDefaults(defineProps<{
    showUserInfo?: boolean
  }>(), {
    showUserInfo: true,
  })

  const user = computed<PartnerInfo>(() => store.state?.Auth?.userInfo)

  const msgDrawerVisible = ref(false)

</script>

<style lang="stylus" scoped>
.app-header
  position fixed
  width 100%
  left 0
  top 0
  background-color #f7f7f7
  transition background-color .18s
  &:hover
    background-color white !important
.life-app-content
  height 48px
  display flex
  align-items center
  justify-content space-between
  box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.03);
  padding: 20px
.logo-wrap
  display flex
  align-items center
  gap 8px
.logo
  height 30px
  width 67px
.user-info
  display flex
  align-items center
  gap 8px
</style>

<template>
  <Cascader
    v-model="value"
    :options="options"
    :multiple="multiple"
    :leaf-only="leafOnly"
    :placeholder="placeholder"
    :field-names="{
      label: 'name',
      value: 'code',
      children: 'children',
    }"
    :max-tag-count="4"
    clearable
  />
</template>
<script setup lang="tsx">
  import { computed, ref } from 'vue'
  import { Cascader2 as Cascader } from '@xhs/delight'
  import { getQueryRegion } from 'shared/services/region'

  const props = withDefaults(defineProps<{
    modelValue?: any[]
    onlyProvinceAndCity?: boolean
    multiple?: boolean
    leafOnly?: boolean
    placeholder?: string
  }>(), {
    onlyProvinceAndCity: true,
    multiple: false,
    leafOnly: true,
    placeholder: '请选择',
  })

  const emit = defineEmits(['update:modelValue'])

  const options = ref<any[]>([])
  const value = computed({
    get: () => props.modelValue,
    set: (v: any) => {
      emit('update:modelValue', v)
    }
  })

  const fetchRegion = async () => {
    const res = await getQueryRegion()
    options.value = res?.regionList?.map(item => ({
      ...item,
      isLeaf: item.leaf,
      children: item.children?.map(child => ({
        ...child,
        isLeaf: true,
        children: [],
      })),
    }))
  }

  fetchRegion()
</script>

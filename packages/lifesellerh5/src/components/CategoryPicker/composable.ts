import { ref, computed, watch } from 'vue'
import { getMarketableCategories } from '~/services/edith_get_marketable_categories'
import { getChildrenCategories } from '~/services/edith_get_children_categories'
import { ICategory, PickerOption, CategoryPickerValue } from './types'

export function useCategoryPicker(props: any, emit: any) {
  // 响应式状态
  const loading = ref(false)
  const error = ref('')
  const visible = ref(false)

  // 分类数据
  const level1Categories = ref<ICategory[]>([])
  const level2Categories = ref<ICategory[]>([])
  const level3Categories = ref<ICategory[]>([])

  // 当前选中值
  const currentValue = ref<CategoryPickerValue | null>(null)

  // 初始化数据
  const initializeData = async () => {
    try {
      loading.value = true
      error.value = ''

      const response = await getMarketableCategories({})
      console.log('response', response)

      // 检查响应数据结构
      if (!response) {
        throw new Error('API 响应为空')
      }

      // 根据实际API返回格式处理数据
      level1Categories.value = response.itemInfos || []
    } catch (err: any) {
      error.value = err.message || '加载分类数据失败'
    } finally {
      loading.value = false
    }
  }

  // 根据初始值加载对应的子分类
  const initializeFromValue = async (value: CategoryPickerValue) => {
    if (!value.level1) return

    try {
      // 确保一级分类已加载
      if (level1Categories.value.length === 0) {
        await initializeData()
      }

      // 加载二级分类
      if (value.level1) {
        const level2Response = await getChildrenCategories({ categoryId: value.level1 })
        level2Categories.value = level2Response.childrenInfos || []
      }

      // 加载三级分类
      if (value.level2) {
        const level3Response = await getChildrenCategories({ categoryId: value.level2 })
        level3Categories.value = level3Response.childrenInfos || []
      }
    } catch (err) {
      error.value = '初始化分类数据失败'
    }
  }

  // 根据categoryId初始化完整路径
  const initializeFromCategoryId = async (categoryId: string) => {
    try {
      loading.value = true
      error.value = ''

      // 由于API不支持根据categoryId获取路径，暂时只设置categoryId
      // 实际项目中可能需要其他方式获取完整路径
      const newValue: CategoryPickerValue = {
        categoryId,
        categoryPath: '未知分类路径'
      }

      currentValue.value = newValue
    } catch (err: any) {
      error.value = err.message || '初始化分类数据失败'
    } finally {
      loading.value = false
    }
  }

  // 监听外部值变化
  watch(
    () => props.modelValue,
    val => {
      currentValue.value = val
      if (val) {
        // 如果只有categoryId，需要通过API获取完整路径
        if (val.categoryId && !val.level1) {
          initializeFromCategoryId(val.categoryId)
        } else {
          initializeFromValue(val)
        }
      }
    },
    { immediate: true }
  )

  // 构建Picker的columns数据
  const columns = computed(() => {
    const cols: PickerOption[][] = []

    // 第一列：一级分类
    if (level1Categories.value.length > 0) {
      cols.push(level1Categories.value.map(item => ({
        label: item.name || '',
        value: item.id || ''
      })).filter(item => item.label && item.value))
    }

    // 第二列：二级分类
    if (level2Categories.value.length > 0) {
      cols.push(level2Categories.value.map(item => ({
        label: item.name || '',
        value: item.id || ''
      })).filter(item => item.label && item.value))
    }

    // 第三列：三级分类
    if (level3Categories.value.length > 0) {
      cols.push(level3Categories.value.map(item => ({
        label: item.name || '',
        value: item.id || ''
      })).filter(item => item.label && item.value))
    }

    return cols
  })

  // 当前选中的值数组（用于Picker组件）
  const pickerValue = computed(() => {
    const values: string[] = []
    if (currentValue.value?.level1) values.push(currentValue.value.level1)
    if (currentValue.value?.level2) values.push(currentValue.value.level2)
    if (currentValue.value?.level3) values.push(currentValue.value.level3)
    return values
  })

  // 显示文本
  const displayText = computed(() => {
    if (!currentValue.value) return props.placeholder || '请选择分类'
    return currentValue.value.categoryPath || '请选择分类'
  })

  // 处理Picker值变化
  const handlePickerChange = async (values: string[], columnIndex: number) => {
    try {
      loading.value = true
      error.value = ''

      const newValue: CategoryPickerValue = { ...currentValue.value }

      // 更新对应级别的值
      if (columnIndex === 0) {
        // 一级分类变化
        newValue.level1 = values[0]
        newValue.level1Name = level1Categories.value.find(item => item.id === values[0])?.name

        // 清空下级分类
        newValue.level2 = undefined
        newValue.level2Name = undefined
        newValue.level3 = undefined
        newValue.level3Name = undefined
        level2Categories.value = []
        level3Categories.value = []

        // 加载二级分类
        if (values[0]) {
          const category = level1Categories.value.find(item => item.id === values[0])
          if (category && !category.isLeaf) {
            const response = await getChildrenCategories({ categoryId: values[0] })
            level2Categories.value = response.childrenInfos || []
          }
        }
      } else if (columnIndex === 1) {
        // 二级分类变化
        newValue.level2 = values[1]
        newValue.level2Name = level2Categories.value.find(item => item.id === values[1])?.name

        // 清空三级分类
        newValue.level3 = undefined
        newValue.level3Name = undefined
        level3Categories.value = []

        // 加载三级分类
        if (values[1]) {
          const category = level2Categories.value.find(item => item.id === values[1])
          if (category && !category.isLeaf) {
            const response = await getChildrenCategories({ categoryId: values[1] })
            level3Categories.value = response.childrenInfos || []
          }
        }
      } else if (columnIndex === 2) {
        // 三级分类变化
        newValue.level3 = values[2]
        newValue.level3Name = level3Categories.value.find(item => item.id === values[2])?.name
      }

      // 更新分类路径和最终分类ID
      updateCategoryPath(newValue)
      currentValue.value = newValue
    } catch (err: any) {
      error.value = err.message || '加载分类数据失败'
    } finally {
      loading.value = false
    }
  }

  // 更新分类路径和最终分类ID
  const updateCategoryPath = (value: CategoryPickerValue) => {
    const pathParts: string[] = []

    if (value.level1Name) pathParts.push(value.level1Name)
    if (value.level2Name) pathParts.push(value.level2Name)
    if (value.level3Name) pathParts.push(value.level3Name)

    value.categoryPath = pathParts.join(' / ')

    // 设置最终的分类ID（优先使用最深层级的ID）
    value.categoryId = value.level3 || value.level2 || value.level1
  }

  // 确认选择
  const handleConfirm = () => {
    emit('update:modelValue', currentValue.value)
    emit('change', currentValue.value)
    visible.value = false
  }

  // 取消选择
  const handleCancel = () => {
    visible.value = false
  }

  // 重试加载数据
  const retryLoad = () => {
    error.value = ''
    initializeData()
  }

  // 显示选择器
  const showPicker = () => {
    if (!props.disabled && !props.readonly) {
      visible.value = true
      if (level1Categories.value.length === 0) {
        initializeData()
      }
    }
  }

  return {
    loading,
    error,
    visible,
    columns,
    pickerValue,
    displayText,
    currentValue,
    initializeData,
    handlePickerChange,
    handleConfirm,
    handleCancel,
    showPicker,
    retryLoad
  }
}

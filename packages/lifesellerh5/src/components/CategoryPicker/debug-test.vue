<template>
  <div class="debug-test-page">
    <h2>CategoryPicker 文字回显调试测试</h2>
    
    <!-- 测试区域 -->
    <div class="test-section">
      <h3>文字回显测试</h3>
      
      <div class="picker-container">
        <CategoryPicker
          v-model="testValue"
          placeholder="请选择主营品类"
          title="选择经营类目"
          @change="handleChange"
        />
      </div>
      
      <!-- 调试信息显示 -->
      <div class="debug-info">
        <h4>调试信息:</h4>
        <div class="debug-item">
          <strong>当前值 (testValue):</strong>
          <pre>{{ JSON.stringify(testValue, null, 2) }}</pre>
        </div>
        
        <div class="debug-item">
          <strong>分类路径 (categoryPath):</strong>
          <span class="path-display">{{ testValue?.categoryPath || '无' }}</span>
        </div>
        
        <div class="debug-item">
          <strong>最后变化时间:</strong>
          <span>{{ lastChangeTime }}</span>
        </div>
        
        <div class="debug-item">
          <strong>变化次数:</strong>
          <span>{{ changeCount }}</span>
        </div>
      </div>
      
      <!-- 控制按钮 -->
      <div class="controls">
        <button @click="clearValue">清空值</button>
        <button @click="setTestValue1">设置测试值1</button>
        <button @click="setTestValue2">设置测试值2</button>
        <button @click="setPartialValue">设置部分值</button>
      </div>
    </div>

    <!-- 期望行为说明 -->
    <div class="expected-behavior">
      <h3>期望行为验证清单:</h3>
      <ul>
        <li>✅ 无选择时：显示placeholder文字"请选择主营品类"，应用placeholder样式</li>
        <li>✅ 有选择时：显示完整分类路径"美食 / 川菜 / 火锅"，不应用placeholder样式</li>
        <li>✅ 选择完成后：点击"确定"按钮，弹窗关闭，触发器显示选择的分类文字</li>
        <li>✅ 只读模式：显示分类文字，不显示箭头图标</li>
        <li>✅ 禁用模式：显示分类文字，应用禁用样式</li>
      </ul>
    </div>

    <!-- 状态测试 -->
    <div class="state-tests">
      <h3>不同状态测试:</h3>
      
      <div class="state-test">
        <h4>只读模式:</h4>
        <CategoryPicker
          v-model="readonlyValue"
          :readonly="true"
          placeholder="只读状态"
        />
      </div>
      
      <div class="state-test">
        <h4>禁用模式:</h4>
        <CategoryPicker
          v-model="disabledValue"
          :disabled="true"
          placeholder="禁用状态"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import CategoryPicker from './index.vue'
  import type { CategoryPickerValue } from './types'

  // 测试数据
  const testValue = ref<CategoryPickerValue | null>(null)
  const lastChangeTime = ref<string>('')
  const changeCount = ref<number>(0)

  // 只读和禁用状态测试值
  const readonlyValue = ref<CategoryPickerValue>({
    level1: '1001',
    level1Name: '美食',
    level2: '1002',
    level2Name: '川菜',
    level3: '1003',
    level3Name: '火锅',
    categoryId: '1003',
    categoryPath: '美食 / 川菜 / 火锅'
  })

  const disabledValue = ref<CategoryPickerValue>({
    level1: '2001',
    level1Name: '休闲娱乐',
    level2: '2002',
    level2Name: 'KTV',
    categoryId: '2002',
    categoryPath: '休闲娱乐 / KTV'
  })

  // 事件处理
  const handleChange = (value: CategoryPickerValue | null) => {
    console.log('=== CategoryPicker Change Event ===')
    console.log('新值:', value)
    console.log('分类路径:', value?.categoryPath)
    console.log('===================================')
    
    lastChangeTime.value = new Date().toLocaleTimeString()
    changeCount.value++
  }

  // 控制函数
  const clearValue = () => {
    testValue.value = null
    console.log('清空值')
  }

  const setTestValue1 = () => {
    testValue.value = {
      level1: '1001',
      level1Name: '美食',
      level2: '1002',
      level2Name: '川菜',
      level3: '1003',
      level3Name: '火锅',
      categoryId: '1003',
      categoryPath: '美食 / 川菜 / 火锅'
    }
    console.log('设置测试值1')
  }

  const setTestValue2 = () => {
    testValue.value = {
      level1: '2001',
      level1Name: '休闲娱乐',
      level2: '2002',
      level2Name: 'KTV',
      categoryId: '2002',
      categoryPath: '休闲娱乐 / KTV'
    }
    console.log('设置测试值2')
  }

  const setPartialValue = () => {
    testValue.value = {
      level1: '3001',
      level1Name: '丽人',
      categoryId: '3001',
      categoryPath: '丽人'
    }
    console.log('设置部分值')
  }
</script>

<style scoped lang="stylus">
.debug-test-page
  padding 20px
  max-width 1200px
  margin 0 auto
  font-family Arial, sans-serif

.test-section, .expected-behavior, .state-tests
  margin-bottom 40px
  padding 20px
  border 1px solid #e0e0e0
  border-radius 8px
  background #fafafa

h2, h3, h4
  color #333
  margin-bottom 16px

h3
  border-bottom 2px solid #1890ff
  padding-bottom 8px

.picker-container
  margin-bottom 20px

.debug-info
  background #f5f5f5
  padding 16px
  border-radius 6px
  margin-bottom 20px

.debug-item
  margin-bottom 12px
  
  strong
    color #666
    display inline-block
    width 200px

.path-display
  background #e6f7ff
  padding 4px 8px
  border-radius 4px
  font-family monospace

pre
  background #fff
  padding 8px
  border-radius 4px
  font-size 12px
  line-height 1.4
  margin 4px 0
  border 1px solid #ddd

.controls
  display flex
  gap 12px
  flex-wrap wrap

button
  padding 8px 16px
  background #1890ff
  color white
  border none
  border-radius 4px
  cursor pointer
  font-size 14px
  
  &:hover
    background #40a9ff
    
  &:active
    background #096dd9

.expected-behavior ul
  list-style-type none
  padding 0
  
  li
    padding 8px 0
    border-bottom 1px solid #eee
    
    &:last-child
      border-bottom none

.state-tests .state-test
  margin-bottom 20px
  padding 16px
  background #fff
  border-radius 6px
  border 1px solid #ddd
</style>

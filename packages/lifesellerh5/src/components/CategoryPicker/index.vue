<template>
  <div class="category-picker-wrapper">
    <!-- 触发器 -->
    <div
      class="category-picker-trigger"
      :class="{
        'disabled': disabled,
        'readonly': readonly,
        'has-value': currentValue
      }"
      @click="showPicker"
    >
      <div class="trigger-content">
        <span class="trigger-text" :class="{ 'placeholder': !currentValue || !currentValue.categoryPath }">
          {{ displayText }}
        </span>
        <OnixIcon
          v-if="!readonly && !disabled"
          name="arrowRightRightM"
          class="trigger-icon"
        />
      </div>
    </div>

    <!-- Picker 选择器 -->
    <Picker
      :columns="columns"
      :value="pickerValue"
      :visible="visible"
      :loading="loading"
      :cancel="true"
      :close="true"
      :close-type="SheetsType.SheetsActionType.text"
      :cancel-type="SheetsType.SheetsActionType.text"
      :cancel-text="'取消'"
      :confirm-text="'确定'"
      :label="title || '选择主营品类'"
      @change="handlePickerChange"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />

    <!-- 错误提示 -->
    <div v-if="error" class="error-message">
      <span>{{ error }}</span>
      <button class="retry-button" @click="retryLoad">重试</button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Picker, SheetsType } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'
  import { useCategoryPicker } from './composable'
  import type { CategoryPickerProps, CategoryPickerEmits } from './types'

  // 静态资源 - 请根据项目实际路径调整
  // import '~/assets/svg/arrowRightRightM.svg'

  // Props
  const props = withDefaults(defineProps<CategoryPickerProps>(), {
    modelValue: null,
    placeholder: '请选择主营品类',
    disabled: false,
    readonly: false,
    title: '选择主营品类'
  })

  // Emits
  const emit = defineEmits<CategoryPickerEmits>()

  // 使用组合式函数
  const {
    loading,
    error,
    visible,
    columns,
    pickerValue,
    displayText,
    currentValue,
    handlePickerChange,
    handleConfirm,
    handleCancel,
    showPicker,
    retryLoad
  } = useCategoryPicker(props, emit)
</script>

<style scoped lang="stylus">
.category-picker-wrapper
  width 100%

.category-picker-trigger
  display flex
  align-items center
  justify-content space-between
  padding 16px
  background var(--bg, #ffffff)
  border 1px solid var(--border-color, #e0e0e0)
  border-radius 8px
  cursor pointer
  transition all 0.2s ease

  &:hover:not(.disabled):not(.readonly)
    border-color var(--primary-color, #1890ff)

  &.disabled
    background var(--bg-disabled, #f5f5f5)
    cursor not-allowed
    opacity 0.6

  &.readonly
    cursor default
    background var(--bg-readonly, #fafafa)

.trigger-content
  display flex
  align-items center
  justify-content space-between
  width 100%

.trigger-text
  font-size 16px
  line-height 1.4
  color var(--text-color, #333333)
  flex 1

  &.placeholder
    color var(--placeholder-color, #999999)

.trigger-icon
  font-size 16px
  color var(--icon-color, #666666)
  margin-left 8px
  transition transform 0.2s ease

.error-message
  margin-top 8px
  padding 8px 12px
  color #f56565
  background-color rgba(245, 101, 101, 0.1)
  border-radius 6px
  font-size 14px
  line-height 1.4
  border-left 3px solid #f56565
  display flex
  align-items center
  justify-content space-between

.retry-button
  background var(--primary-color, #1890ff)
  color white
  border none
  border-radius 4px
  padding 4px 8px
  font-size 12px
  cursor pointer
  margin-left 8px

  &:hover
    background var(--primary-color-hover, #40a9ff)
</style>

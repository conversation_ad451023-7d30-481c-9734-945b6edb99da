<template>
  <div class="test-page">
    <h2>CategoryPicker 优化测试页面</h2>
    
    <!-- 无默认值测试 -->
    <div class="test-section">
      <h3>1. 无默认值测试（应自动选择第一个分类）</h3>
      <CategoryPicker
        v-model="noDefaultValue"
        placeholder="无默认值，应自动选择"
        @change="handleNoDefaultChange"
      />
      
      <div class="result">
        <h4>当前值:</h4>
        <pre>{{ JSON.stringify(noDefaultValue, null, 2) }}</pre>
      </div>
    </div>

    <!-- 有默认值测试 -->
    <div class="test-section">
      <h3>2. 有默认值测试（应正确回显）</h3>
      <CategoryPicker
        v-model="withDefaultValue"
        placeholder="有默认值，应正确回显"
        @change="handleWithDefaultChange"
      />
      
      <div class="result">
        <h4>当前值:</h4>
        <pre>{{ JSON.stringify(withDefaultValue, null, 2) }}</pre>
      </div>
      
      <div class="controls">
        <button @click="setDefaultValue">设置默认值</button>
        <button @click="clearDefaultValue">清空默认值</button>
      </div>
    </div>

    <!-- 只有categoryId的测试 -->
    <div class="test-section">
      <h3>3. 只有categoryId测试</h3>
      <CategoryPicker
        v-model="categoryIdOnlyValue"
        placeholder="只有categoryId"
        @change="handleCategoryIdOnlyChange"
      />
      
      <div class="result">
        <h4>当前值:</h4>
        <pre>{{ JSON.stringify(categoryIdOnlyValue, null, 2) }}</pre>
      </div>
      
      <div class="controls">
        <button @click="setCategoryIdOnly">设置categoryId</button>
      </div>
    </div>

    <!-- 动态切换测试 -->
    <div class="test-section">
      <h3>4. 动态切换测试</h3>
      <CategoryPicker
        v-model="dynamicValue"
        placeholder="动态切换测试"
        @change="handleDynamicChange"
      />
      
      <div class="result">
        <h4>当前值:</h4>
        <pre>{{ JSON.stringify(dynamicValue, null, 2) }}</pre>
      </div>
      
      <div class="controls">
        <button @click="switchToNull">切换到null</button>
        <button @click="switchToDefault">切换到默认值</button>
        <button @click="switchToCategoryId">切换到categoryId</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import CategoryPicker from './index.vue'
  import type { CategoryPickerValue } from './types'

  // 无默认值测试
  const noDefaultValue = ref<CategoryPickerValue | null>(null)

  // 有默认值测试
  const withDefaultValue = ref<CategoryPickerValue | null>(null)

  // 只有categoryId测试
  const categoryIdOnlyValue = ref<CategoryPickerValue | null>(null)

  // 动态切换测试
  const dynamicValue = ref<CategoryPickerValue | null>(null)

  // 事件处理
  const handleNoDefaultChange = (value: CategoryPickerValue | null) => {
    console.log('无默认值变化:', value)
  }

  const handleWithDefaultChange = (value: CategoryPickerValue | null) => {
    console.log('有默认值变化:', value)
  }

  const handleCategoryIdOnlyChange = (value: CategoryPickerValue | null) => {
    console.log('只有categoryId变化:', value)
  }

  const handleDynamicChange = (value: CategoryPickerValue | null) => {
    console.log('动态切换变化:', value)
  }

  // 控制函数
  const setDefaultValue = () => {
    withDefaultValue.value = {
      level1: '1001',
      level1Name: '美食',
      level2: '1002', 
      level2Name: '川菜',
      level3: '1003',
      level3Name: '火锅',
      categoryId: '1003',
      categoryPath: '美食 / 川菜 / 火锅'
    }
  }

  const clearDefaultValue = () => {
    withDefaultValue.value = null
  }

  const setCategoryIdOnly = () => {
    categoryIdOnlyValue.value = {
      categoryId: '1003'
    }
  }

  const switchToNull = () => {
    dynamicValue.value = null
  }

  const switchToDefault = () => {
    dynamicValue.value = {
      level1: '2001',
      level1Name: '休闲娱乐',
      level2: '2002',
      level2Name: 'KTV',
      categoryId: '2002',
      categoryPath: '休闲娱乐 / KTV'
    }
  }

  const switchToCategoryId = () => {
    dynamicValue.value = {
      categoryId: '3001'
    }
  }
</script>

<style scoped lang="stylus">
.test-page
  padding 20px
  max-width 1000px
  margin 0 auto

.test-section
  margin-bottom 40px
  padding 20px
  border 1px solid #e0e0e0
  border-radius 8px
  background #fafafa

h2, h3
  color #333

h3
  border-bottom 2px solid #1890ff
  padding-bottom 8px

.result
  margin-top 16px
  padding 12px
  background #f5f5f5
  border-radius 6px
  border-left 4px solid #1890ff

pre
  margin 0
  font-size 12px
  line-height 1.4
  color #333
  white-space pre-wrap
  word-break break-all

.controls
  margin-top 16px
  display flex
  gap 12px
  flex-wrap wrap

button
  padding 8px 16px
  background #1890ff
  color white
  border none
  border-radius 4px
  cursor pointer
  font-size 14px
  
  &:hover
    background #40a9ff
    
  &:active
    background #096dd9
</style>

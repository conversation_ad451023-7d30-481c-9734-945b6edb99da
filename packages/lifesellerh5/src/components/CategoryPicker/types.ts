/**
 * CategoryPicker 组件类型定义
 */

// 分类数据接口
export interface ICategory {
  id: string
  name: string
  is_leaf: string
  level: number
}

// Picker 选项接口
export interface PickerOption {
  label: string
  value: string
  children?: PickerOption[]
}

// 选中值类型
export interface CategoryPickerValue {
  level1?: string // 一级分类ID
  level2?: string // 二级分类ID
  level3?: string // 三级分类ID
  level1Name?: string // 一级分类名称
  level2Name?: string // 二级分类名称
  level3Name?: string // 三级分类名称
  categoryId?: string // 最终选中的分类ID（叶子节点）
  categoryPath?: string // 分类路径，如 "美食/川菜/火锅"
}

// 组件 Props
export interface CategoryPickerProps {
  modelValue?: CategoryPickerValue | null
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  title?: string
}

// 组件 Emits
export interface CategoryPickerEmits {
  'update:modelValue': [value: CategoryPickerValue | null]
  'change': [value: CategoryPickerValue | null]
}

// API 响应类型
export interface GetMarketableCategoriesResponse {
  itemInfos: ICategory[]
}

export interface GetChildrenCategoriesResponse {
  childrenInfos: ICategory[]
  paths: ICategory[]
}

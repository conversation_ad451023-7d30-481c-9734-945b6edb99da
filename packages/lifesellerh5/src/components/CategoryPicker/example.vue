<template>
  <div class="category-picker-example">
    <h2>CategoryPicker 组件示例</h2>

    <!-- 基础用法 -->
    <section class="example-section">
      <h3>基础用法</h3>
      <CategoryPicker
        v-model="basicValue"
        placeholder="请选择主营品类"
        @change="handleBasicChange"
      />
      <div class="result">
        <h4>当前值:</h4>
        <pre>{{ JSON.stringify(basicValue, null, 2) }}</pre>
      </div>
    </section>

    <!-- 设置初始值 -->
    <section class="example-section">
      <h3>设置初始值（完整路径）</h3>
      <CategoryPicker
        v-model="initialValue"
        title="选择经营类目"
        @change="handleInitialChange"
      />
      <div class="result">
        <h4>当前值:</h4>
        <pre>{{ JSON.stringify(initialValue, null, 2) }}</pre>
      </div>
    </section>

    <!-- 通过 categoryId 初始化 -->
    <section class="example-section">
      <h3>通过 categoryId 初始化</h3>
      <CategoryPicker
        v-model="categoryIdValue"
        placeholder="自动获取分类路径"
        @change="handleCategoryIdChange"
      />
      <div class="result">
        <h4>当前值:</h4>
        <pre>{{ JSON.stringify(categoryIdValue, null, 2) }}</pre>
      </div>
      <button @click="setCategoryId">设置 categoryId</button>
    </section>

    <!-- 只读状态 -->
    <section class="example-section">
      <h3>只读状态</h3>
      <CategoryPicker
        v-model="readonlyValue"
        :readonly="true"
        placeholder="只读状态"
      />
    </section>

    <!-- 禁用状态 -->
    <section class="example-section">
      <h3>禁用状态</h3>
      <CategoryPicker
        v-model="disabledValue"
        :disabled="true"
        placeholder="禁用状态"
      />
    </section>

    <!-- 自定义标题 -->
    <section class="example-section">
      <h3>自定义标题</h3>
      <CategoryPicker
        v-model="customTitleValue"
        title="请选择您的经营类目"
        placeholder="点击选择分类"
        @change="handleCustomTitleChange"
      />
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import CategoryPicker from './index.vue'
  import type { CategoryPickerValue } from './types'

  // 基础用法
  const basicValue = ref<CategoryPickerValue | null>(null)

  // 设置初始值（完整路径）
  const initialValue = ref<CategoryPickerValue>({
    level1: '1001',
    level1Name: '美食',
    level2: '1002',
    level2Name: '川菜',
    level3: '1003',
    level3Name: '火锅',
    categoryId: '1003',
    categoryPath: '美食 / 川菜 / 火锅'
  })

  // 通过 categoryId 初始化
  const categoryIdValue = ref<CategoryPickerValue | null>(null)

  // 只读状态
  const readonlyValue = ref<CategoryPickerValue>({
    level1: '2001',
    level1Name: '休闲娱乐',
    level2: '2002',
    level2Name: 'KTV',
    categoryId: '2002',
    categoryPath: '休闲娱乐 / KTV'
  })

  // 禁用状态
  const disabledValue = ref<CategoryPickerValue>({
    level1: '3001',
    level1Name: '丽人',
    level2: '3002',
    level2Name: '美发',
    categoryId: '3002',
    categoryPath: '丽人 / 美发'
  })

  // 自定义标题
  const customTitleValue = ref<CategoryPickerValue | null>(null)

  // 事件处理
  const handleBasicChange = (value: CategoryPickerValue | null) => {
    console.log('基础用法值变化:', value)
  }

  const handleInitialChange = (value: CategoryPickerValue | null) => {
    console.log('初始值变化:', value)
  }

  const handleCategoryIdChange = (value: CategoryPickerValue | null) => {
    console.log('categoryId 值变化:', value)
  }

  const handleCustomTitleChange = (value: CategoryPickerValue | null) => {
    console.log('自定义标题值变化:', value)
  }

  // 设置 categoryId 示例
  const setCategoryId = () => {
    categoryIdValue.value = {
      categoryId: '1003' // 假设这是一个有效的分类ID
    }
  }
</script>

<style scoped lang="stylus">
.category-picker-example
  padding 20px
  max-width 800px
  margin 0 auto

.example-section
  margin-bottom 40px
  padding 20px
  border 1px solid #e0e0e0
  border-radius 8px
  background #fafafa

h2
  color #333
  margin-bottom 30px
  text-align center

h3
  color #666
  margin-bottom 20px
  border-bottom 2px solid #1890ff
  padding-bottom 8px

h4
  color #888
  margin 16px 0 8px 0
  font-size 14px

.result
  margin-top 16px
  padding 12px
  background #f5f5f5
  border-radius 6px
  border-left 4px solid #1890ff

pre
  margin 0
  font-size 12px
  line-height 1.4
  color #333
  white-space pre-wrap
  word-break break-all

button
  margin-top 12px
  padding 8px 16px
  background #1890ff
  color white
  border none
  border-radius 4px
  cursor pointer
  font-size 14px

  &:hover
    background #40a9ff
</style>

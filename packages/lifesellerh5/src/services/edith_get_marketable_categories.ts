/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
  * @XHS_API_KIT-INFO
  *
  * @id: 53555
  * @name: 本地生活移动端（主端）-商家管理-获取商家可售类目接口
  * @identifier: app.api.redlife.life_service.seller.get_marketable_categories.get
  * @version: undefined
  * @path: /app/api/redlife/life_service/seller/get_marketable_categories
  * @method: get
  * @description: 本地生活-商家管理-获取商家可售类目接口
  *
  * @XHS_API_KIT-INFO
*/

import { http } from '@xhs/launcher'
import { env } from 'shared/config/env.config'


const hostMap = {
  test: 'http://www.edith-mobile.sl.sit.xiaohongshu.com',
  development: 'http://www.edith-mobile.sl.sit.xiaohongshu.com',
  prerelease: 'https://www.edith-mobile.sl.sit.xiaohongshu.com',
  production: 'https://www.edith.xiaohongshu.com',
}

const host = hostMap[env] || 'https://www.xiaohongshu.com'

export interface IGetMarketableCategoriesPayload {
	/** 商家ID */
	sellerId?: string
}

export interface IItemInfo {
	/** 类目ID */
	id?: string
	/** 类目名称 */
	name?: string
	/** 类目层级 */
	level?: number
	/** 是否叶子节点 */
	isLeaf?: boolean
}

export interface IData {
	/** 可售类目列表 */
	itemInfos?: IItemInfo[]
}

export interface IGetMarketableCategoriesResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getMarketableCategories(params: IGetMarketableCategoriesPayload, options = {}): Promise<IData> {
  return http.get(`http://life-mobile.sl.sit.xiaohongshu.com/app/api/redlife/life_service/seller/get_marketable_categories`, { params, transform: true, ...options })
}

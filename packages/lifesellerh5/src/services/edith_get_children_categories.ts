/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
  * @XHS_API_KIT-INFO
  *
  * @id: 53556
  * @name: 本地生活移动端（主端）-商家管理-获取类目子类目列表接口
  * @identifier: app.api.redlife.life_service.seller.get_children_categories.get
  * @version: undefined
  * @path: /app/api/redlife/life_service/seller/get_children_categories
  * @method: get
  * @description: 本地生活-商家管理-获取类目子类目列表接口
  *
  * @XHS_API_KIT-INFO
*/

import { http } from '@xhs/launcher'

export interface IGetChildrenCategoriesPayload {
	/** 类目ID */
	categoryId?: string
	/** 商家ID */
	sellerId?: string
}

export interface IChildrenInfo {
	/** 类目ID */
	id?: string
	/** 类目名称 */
	name?: string
	/** 类目层级 */
	level?: number
	/** 是否叶子节点 */
	isLeaf?: boolean
}

export interface IData {
	/** 子类目列表 */
	childrenInfos?: IChildrenInfo[]
}

export interface IGetChildrenCategoriesResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getChildrenCategories(params: IGetChildrenCategoriesPayload, options = {}): Promise<IData> {
  return http.get('/app/api/redlife/life_service/seller/get_children_categories', { params, transform: true, ...options })
}
